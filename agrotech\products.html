<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agricultural Equipment Products - a.agrotech | Find the Right Farming Equipment</title>
    <meta name="description" content="Browse comprehensive collection of agricultural equipment. Compare specifications, prices, and connect with verified dealers across India.">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.html" class="logo">
                        <i class="fas fa-seedling"></i>
                        <span>a.agrotech</span>
                    </a>
                </div>
                
                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle" aria-label="Toggle menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                
                <!-- Navigation Menu -->
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.html">Home</a></li>
                        <li class="dropdown">
                            <a href="products.html" class="active">Products <i class="fas fa-chevron-down"></i></a>
                            <div class="dropdown-menu">
                                <div class="dropdown-section">
                                    <h4>Shop by Category</h4>
                                    <a href="products.html?category=tillage">Tillage Equipment</a>
                                    <a href="products.html?category=sowing">Sowing & Planting</a>
                                    <a href="products.html?category=protection">Crop Protection</a>
                                    <a href="products.html?category=harvesting">Harvesting</a>
                                </div>
                                <div class="dropdown-section">
                                    <h4>Shop by Brand</h4>
                                    <a href="products.html?brand=mahindra">Mahindra</a>
                                    <a href="products.html?brand=sonalika">Sonalika</a>
                                    <a href="products.html?brand=johndeere">John Deere</a>
                                </div>
                                <div class="dropdown-section">
                                    <h4>Shop by Crop</h4>
                                    <a href="products.html?crop=paddy">Paddy Solutions</a>
                                    <a href="products.html?crop=wheat">Wheat Solutions</a>
                                    <a href="products.html?crop=sugarcane">Sugarcane Solutions</a>
                                </div>
                            </div>
                        </li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="photo-gallery.html">Gallery</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                    
                    <!-- Search Bar -->
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search equipment..." id="searchInput">
                        <button class="search-btn" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                        <div class="search-suggestions" id="searchSuggestions"></div>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="header-contact">
                        <a href="tel:1800-123-4567" class="toll-free">
                            <i class="fas fa-phone"></i>
                            <span>1800-123-4567</span>
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 id="pageTitle">Agricultural Equipment</h1>
                <p id="pageDescription">Find the perfect equipment for your farming needs</p>
                <nav class="breadcrumb">
                    <a href="index.html">Home</a>
                    <span>/</span>
                    <span id="breadcrumbCurrent">Products</span>
                </nav>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-section">
        <div class="container">
            <div class="products-layout">
                <!-- Filters Sidebar -->
                <aside class="filters-sidebar">
                    <div class="filters-header">
                        <h3>Filter Products</h3>
                        <button class="clear-filters" onclick="clearAllFilters()">
                            <i class="fas fa-times"></i>
                            Clear All
                        </button>
                    </div>
                    
                    <!-- Search Filter -->
                    <div class="filter-section">
                        <h4>Search</h4>
                        <div class="search-filter">
                            <input type="text" id="productSearch" class="form-input" placeholder="Search products...">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    
                    <!-- Category Filter -->
                    <div class="filter-section">
                        <h4>Category</h4>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-category" value="tillage">
                                <span class="checkmark"></span>
                                <span class="filter-label">Tillage Equipment</span>
                                <span class="filter-count">(45)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-category" value="sowing">
                                <span class="checkmark"></span>
                                <span class="filter-label">Sowing & Planting</span>
                                <span class="filter-count">(32)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-category" value="protection">
                                <span class="checkmark"></span>
                                <span class="filter-label">Crop Protection</span>
                                <span class="filter-count">(28)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-category" value="harvesting">
                                <span class="checkmark"></span>
                                <span class="filter-label">Harvesting</span>
                                <span class="filter-count">(25)</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Brand Filter -->
                    <div class="filter-section">
                        <h4>Brand</h4>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-brand" value="mahindra">
                                <span class="checkmark"></span>
                                <span class="filter-label">Mahindra</span>
                                <span class="filter-count">(35)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-brand" value="sonalika">
                                <span class="checkmark"></span>
                                <span class="filter-label">Sonalika</span>
                                <span class="filter-count">(28)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-brand" value="johndeere">
                                <span class="checkmark"></span>
                                <span class="filter-label">John Deere</span>
                                <span class="filter-count">(22)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-brand" value="kubota">
                                <span class="checkmark"></span>
                                <span class="filter-label">Kubota</span>
                                <span class="filter-count">(18)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-brand" value="fieldking">
                                <span class="checkmark"></span>
                                <span class="filter-label">Fieldking</span>
                                <span class="filter-count">(15)</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Crop Filter -->
                    <div class="filter-section">
                        <h4>Suitable for Crop</h4>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-crop" value="wheat">
                                <span class="checkmark"></span>
                                <span class="filter-label">Wheat</span>
                                <span class="filter-count">(65)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-crop" value="paddy">
                                <span class="checkmark"></span>
                                <span class="filter-label">Paddy/Rice</span>
                                <span class="filter-count">(58)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-crop" value="sugarcane">
                                <span class="checkmark"></span>
                                <span class="filter-label">Sugarcane</span>
                                <span class="filter-count">(25)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-crop" value="cotton">
                                <span class="checkmark"></span>
                                <span class="filter-label">Cotton</span>
                                <span class="filter-count">(22)</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Power Range Filter -->
                    <div class="filter-section">
                        <h4>Power Required (HP)</h4>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-power" value="20-35">
                                <span class="checkmark"></span>
                                <span class="filter-label">20-35 HP</span>
                                <span class="filter-count">(25)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-power" value="35-50">
                                <span class="checkmark"></span>
                                <span class="filter-label">35-50 HP</span>
                                <span class="filter-count">(35)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-power" value="50-75">
                                <span class="checkmark"></span>
                                <span class="filter-label">50-75 HP</span>
                                <span class="filter-count">(28)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-power" value="75+">
                                <span class="checkmark"></span>
                                <span class="filter-label">75+ HP</span>
                                <span class="filter-count">(15)</span>
                            </label>
                        </div>
                    </div>
                </aside>
                
                <!-- Products Content -->
                <main class="products-content">
                    <!-- Active Filters -->
                    <div class="active-filters" id="activeFilters">
                        <!-- Active filters will be displayed here -->
                    </div>
                    
                    <!-- Products Header -->
                    <div class="products-header">
                        <div class="results-info">
                            <span class="result-count" id="resultCount">Showing 130 products</span>
                        </div>
                        <div class="sort-options">
                            <label for="sortSelect">Sort by:</label>
                            <select id="sortSelect" class="form-select">
                                <option value="relevance">Relevance</option>
                                <option value="name-asc">Name (A-Z)</option>
                                <option value="name-desc">Name (Z-A)</option>
                                <option value="brand-asc">Brand (A-Z)</option>
                                <option value="popular">Most Popular</option>
                            </select>
                        </div>
                        <div class="view-options">
                            <button class="view-btn active" data-view="grid" title="Grid View">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-btn" data-view="list" title="List View">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Products Grid -->
                    <div class="products-grid" id="productsGrid">
                        <!-- Products will be loaded dynamically -->
                    </div>
                    
                    <!-- Load More Button -->
                    <div class="load-more-container">
                        <button class="btn btn-outline" id="loadMoreBtn">
                            <i class="fas fa-plus"></i>
                            Load More Products
                        </button>
                    </div>
                </main>
            </div>
        </div>
    </section>

    <!-- Comparison Bar -->
    <div class="comparison-bar" id="comparisonBar">
        <div class="container">
            <div class="comparison-content">
                <div class="comparison-info">
                    <span class="comparison-count">0</span> products selected for comparison
                </div>
                <div class="comparison-actions">
                    <button class="btn btn-primary" id="compareBtn" disabled>
                        <i class="fas fa-balance-scale"></i>
                        Compare Products
                    </button>
                    <button class="btn btn-outline" id="clearComparisonBtn">
                        <i class="fas fa-times"></i>
                        Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <a href="index.html" class="logo">
                            <i class="fas fa-seedling"></i>
                            <span>a.agrotech</span>
                        </a>
                        <p>Empowering Indian farmers with the right agricultural equipment and trusted dealer connections.</p>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="photo-gallery.html">Gallery</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="products.html?category=tillage">Tillage Equipment</a></li>
                        <li><a href="products.html?category=sowing">Sowing & Planting</a></li>
                        <li><a href="products.html?category=protection">Crop Protection</a></li>
                        <li><a href="products.html?category=harvesting">Harvesting</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> 1800-123-4567 (Toll Free)</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> New Delhi, India</p>
                    </div>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 a.agrotech. All rights reserved. | <a href="#">Privacy Policy</a> | <a href="#">Terms of Service</a></p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Float Button -->
    <a href="https://wa.me/************" class="whatsapp-float" target="_blank" aria-label="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/search.js"></script>
    <script>
        // Initialize products page
        document.addEventListener('DOMContentLoaded', function() {
            initProductsPage();
            loadProducts();
            setupFilters();
            setupSorting();
            setupViewToggle();
            setupComparison();
            handleURLParameters();
        });
        
        // Products page initialization
        function initProductsPage() {
            console.log('Products page initialized');
        }
        
        // Load products (mock data for now)
        function loadProducts() {
            // This would typically fetch from an API
            const mockProducts = generateMockProducts();
            renderProducts(mockProducts, document.getElementById('productsGrid'));
        }
        
        // Generate mock products
        function generateMockProducts() {
            const categories = ['tillage', 'sowing', 'protection', 'harvesting'];
            const brands = ['Mahindra', 'Sonalika', 'John Deere', 'Kubota', 'Fieldking'];
            const crops = ['wheat', 'paddy', 'sugarcane', 'cotton'];
            
            const products = [];
            for (let i = 1; i <= 20; i++) {
                products.push({
                    id: i,
                    name: `Agricultural Equipment ${i}`,
                    brand: brands[Math.floor(Math.random() * brands.length)],
                    category: categories[Math.floor(Math.random() * categories.length)],
                    crop: [crops[Math.floor(Math.random() * crops.length)]],
                    image: `images/products/product-${i}.jpg`,
                    description: `High-quality agricultural equipment for modern farming needs.`,
                    features: ['Feature 1', 'Feature 2', 'Feature 3'],
                    badge: i <= 5 ? 'Popular' : null
                });
            }
            return products;
        }
        
        // Setup filters
        function setupFilters() {
            const filterCheckboxes = document.querySelectorAll('.filter-checkbox');
            filterCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', applyFilters);
            });
            
            const searchFilter = document.getElementById('productSearch');
            if (searchFilter) {
                searchFilter.addEventListener('input', debounce(applyFilters, 300));
            }
        }
        
        // Setup sorting
        function setupSorting() {
            const sortSelect = document.getElementById('sortSelect');
            if (sortSelect) {
                sortSelect.addEventListener('change', applySorting);
            }
        }
        
        // Setup view toggle
        function setupViewToggle() {
            const viewButtons = document.querySelectorAll('.view-btn');
            viewButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    viewButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    const view = this.dataset.view;
                    const productsGrid = document.getElementById('productsGrid');
                    productsGrid.className = view === 'list' ? 'products-list' : 'products-grid';
                });
            });
        }
        
        // Setup comparison
        function setupComparison() {
            // Comparison functionality will be implemented
        }
        
        // Handle URL parameters
        function handleURLParameters() {
            const urlParams = new URLSearchParams(window.location.search);
            
            // Handle category parameter
            const category = urlParams.get('category');
            if (category) {
                const categoryCheckbox = document.querySelector(`.filter-category[value="${category}"]`);
                if (categoryCheckbox) {
                    categoryCheckbox.checked = true;
                    updatePageTitle(category);
                }
            }
            
            // Handle brand parameter
            const brand = urlParams.get('brand');
            if (brand) {
                const brandCheckbox = document.querySelector(`.filter-brand[value="${brand}"]`);
                if (brandCheckbox) {
                    brandCheckbox.checked = true;
                    updatePageTitle(null, brand);
                }
            }
            
            // Handle crop parameter
            const crop = urlParams.get('crop');
            if (crop) {
                const cropCheckbox = document.querySelector(`.filter-crop[value="${crop}"]`);
                if (cropCheckbox) {
                    cropCheckbox.checked = true;
                    updatePageTitle(null, null, crop);
                }
            }
            
            // Apply filters if any parameters were set
            if (category || brand || crop) {
                applyFilters();
            }
        }
        
        // Update page title based on filters
        function updatePageTitle(category, brand, crop) {
            const pageTitle = document.getElementById('pageTitle');
            const pageDescription = document.getElementById('pageDescription');
            const breadcrumbCurrent = document.getElementById('breadcrumbCurrent');
            
            if (category) {
                const categoryNames = {
                    'tillage': 'Tillage Equipment',
                    'sowing': 'Sowing & Planting Equipment',
                    'protection': 'Crop Protection Equipment',
                    'harvesting': 'Harvesting Equipment'
                };
                pageTitle.textContent = categoryNames[category];
                pageDescription.textContent = `Find the best ${categoryNames[category].toLowerCase()} for your farming needs`;
                breadcrumbCurrent.textContent = categoryNames[category];
            } else if (brand) {
                pageTitle.textContent = `${brand} Equipment`;
                pageDescription.textContent = `Explore ${brand} agricultural equipment collection`;
                breadcrumbCurrent.textContent = `${brand} Products`;
            } else if (crop) {
                const cropNames = {
                    'wheat': 'Wheat Farming Equipment',
                    'paddy': 'Paddy/Rice Farming Equipment',
                    'sugarcane': 'Sugarcane Farming Equipment',
                    'cotton': 'Cotton Farming Equipment'
                };
                pageTitle.textContent = cropNames[crop];
                pageDescription.textContent = `Complete equipment solutions for ${crop} farming`;
                breadcrumbCurrent.textContent = cropNames[crop];
            }
        }
        
        // Apply filters function
        function applyFilters() {
            // Filter logic will be implemented here
            console.log('Applying filters...');
        }
        
        // Apply sorting function
        function applySorting() {
            // Sorting logic will be implemented here
            console.log('Applying sorting...');
        }
    </script>
</body>
</html>
